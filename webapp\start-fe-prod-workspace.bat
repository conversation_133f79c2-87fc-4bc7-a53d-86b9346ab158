setlocal
for /f "delims=" %%i in ('node -v') do set "node_version=%%i"
for /f "tokens=2 delims=v." %%i in ("%node_version%") do set "major_version=%%i"
if %major_version% GEQ 17 (
  set "NODE_OPTIONS=--openssl-legacy-provider"
  echo Node.js version is greater than or equal to 17. NODE_OPTIONS has been set to --openssl-legacy-provider.
)
where /q pnpm
if errorlevel 1 (
  echo pnpm is not installed. Installing...
  npm install -g pnpm
  if errorlevel 1 (
    echo Failed to install pnpm. Please check if npm is installed and the network connection is working.
  ) else (
    echo pnpm installed successfully.
  )
) else (
  echo pnpm is already installed.
)

rmdir /S /Q .\packages\hchatdata-fe\src\.umi
rmdir /S /Q .\packages\hchatdata-fe\src\.umi-production

echo Installing workspace dependencies...
call pnpm install

echo Building packages in dependency order...
call pnpm --filter genie-ui build
call pnpm --filter hchatdata-chat-sdk build
call pnpm --filter hchatdata-fe build:os-local

echo Creating archive...
cd ./packages/hchatdata-fe
tar -zcvf hchatdata-webapp.tar.gz hchatdata-webapp
move hchatdata-webapp.tar.gz ..\..\
cd ..\..
endlocal
