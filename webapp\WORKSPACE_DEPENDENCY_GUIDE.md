# Workspace 依赖管理指南

## 问题描述

在执行 `start-fe-prod.bat` 后，发现以下文件被意外修改：

1. `webapp/pnpm-lock.yaml` 中的 `specifier: workspace:*` 被改为 `specifier: ^0.1.0`
2. `webapp/packages/chat-sdk/package.json` 中的 `"genie-ui": "workspace:*"` 被改为 `"genie-ui": "^0.1.0"`

## 问题原因

### 1. pnpm link 命令的副作用
原始脚本中使用了以下命令序列：
```bash
pnpm link --global        # 全局链接 genie-ui
pnpm link genie-ui        # 在 chat-sdk 中链接 genie-ui
pnpm link ../chat-sdk     # 在 hchatdata-fe 中链接 chat-sdk
```

### 2. 依赖解析机制
- `pnpm link` 会将 `workspace:*` 依赖解析为具体版本号
- 后续的 `pnpm install` 会更新 lock 文件，将 workspace 引用替换为具体版本

### 3. Workspace 协议被覆盖
pnpm 的链接机制会干扰 workspace 协议的正常工作，导致依赖关系被错误解析。

## 解决方案

### 方案1：使用 Workspace 协议（推荐）

**优点：**
- 保持 workspace 依赖的完整性
- 自动处理依赖顺序
- 避免版本冲突
- 更好的开发体验

**实施步骤：**

1. **确保 package.json 使用正确的 workspace 依赖：**
   ```json
   {
     "dependencies": {
       "genie-ui": "workspace:*"
     }
   }
   ```

2. **使用新的构建脚本：**
   ```bash
   # 使用 start-fe-prod-workspace.bat 替代原脚本
   .\start-fe-prod-workspace.bat
   ```

3. **利用 pnpm filter 功能：**
   ```bash
   pnpm --filter genie-ui build
   pnpm --filter hchatdata-chat-sdk build
   pnpm --filter hchatdata-fe build:os-local
   ```

### 方案2：使用根目录脚本

在根目录的 `package.json` 中已经定义了构建脚本：

```bash
# 构建所有包
pnpm run build:all

# 或者分别构建
pnpm run build:genie-ui
pnpm run build:chat-sdk
pnpm run build:hchatdata-fe
```

## 最佳实践

### 1. 依赖声明
```json
{
  "dependencies": {
    "genie-ui": "workspace:*",           // 本地 workspace 包
    "external-package": "^1.0.0"        // 外部包使用具体版本
  }
}
```

### 2. 构建顺序
确保按依赖关系顺序构建：
1. `genie-ui` (基础 UI 组件库)
2. `chat-sdk` (依赖 genie-ui)
3. `hchatdata-fe` (依赖 chat-sdk)

### 3. 避免使用 pnpm link
在 workspace 环境中，避免使用 `pnpm link`，而是依赖 workspace 协议的自动解析。

## 文件恢复

如果文件已被修改，可以通过以下方式恢复：

### 1. 恢复 package.json
```bash
# 在 chat-sdk 目录下
cd packages/chat-sdk
# 手动修改 package.json 中的 genie-ui 依赖为 "workspace:*"
```

### 2. 重新生成 lock 文件
```bash
# 在根目录下
rm pnpm-lock.yaml
pnpm install
```

## 验证

构建完成后，验证以下内容：

1. **依赖关系正确：**
   ```bash
   pnpm list --depth=1
   ```

2. **构建产物存在：**
   - `packages/genie-ui/dist/`
   - `packages/chat-sdk/dist/`
   - `packages/hchatdata-fe/hchatdata-webapp/`

3. **Archive 文件生成：**
   - `hchatdata-webapp.tar.gz`

## 总结

通过使用 workspace 协议和正确的构建流程，可以避免依赖关系被意外修改，同时保持项目的一致性和可维护性。
